/**
 * Customizer Controls for PNG Emblem
 *
 * @package Dakoii_Provincial_Government_Theme
 */

(function($) {
    'use strict';

    wp.customize.bind('ready', function() {

        // Add help text and styling to PNG Emblem section
        var emblemSection = wp.customize.section('nols_espa_png_emblem');

        if (emblemSection) {
            emblemSection.container.find('.customize-section-description').append(
                '<div class="png-emblem-help">' +
                '<h4>PNG Emblem Guidelines:</h4>' +
                '<ul>' +
                '<li>• Recommended image size: 100x100 pixels</li>' +
                '<li>• Supported formats: JPG, PNG, GIF, SVG</li>' +
                '<li>• Square images work best</li>' +
                '<li>• Transparent backgrounds are recommended</li>' +
                '</ul>' +
                '</div>'
            );
        }

        // Add help text for color combinations
        var colorSection = wp.customize.section('nols_espa_cultural_colors');

        if (colorSection) {
            colorSection.container.find('.customize-section-description').append(
                '<div class="color-combination-help">' +
                '<h4>Color Combination Guide:</h4>' +
                '<ul>' +
                '<li><strong>Traditional PNG Flag:</strong> Classic black, red, and gold colors</li>' +
                '<li><strong>Sepik River Heritage:</strong> Earth tones inspired by the Sepik River region</li>' +
                '<li><strong>Cultural Celebration:</strong> Vibrant colors for festive occasions</li>' +
                '<li><strong>Custom Colors:</strong> Choose your own individual colors</li>' +
                '</ul>' +
                '<p><em>Tip: Select a predefined combination first, then fine-tune individual colors if needed.</em></p>' +
                '</div>'
            );
        }

        // Show/hide individual color controls based on combination selection
        function toggleColorControls() {
            var combination = wp.customize('color_combination').get();
            var controls = ['png_red_color', 'png_green_color', 'png_yellow_color'];

            controls.forEach(function(controlId) {
                var control = wp.customize.control(controlId);
                if (control) {
                    if (combination === 'custom') {
                        control.container.show();
                    } else {
                        control.container.hide();
                    }
                }
            });
        }

        // Initial toggle
        toggleColorControls();

        // Listen for combination changes
        wp.customize('color_combination', function(value) {
            value.bind(toggleColorControls);
        });

        // Add custom styling for the PNG emblem section
        $('<style>')
            .prop('type', 'text/css')
            .html(`
                .png-emblem-help {
                    background: #f7f7f7;
                    border: 1px solid #ddd;
                    border-radius: 4px;
                    padding: 15px;
                    margin-top: 15px;
                }
                .png-emblem-help h4 {
                    margin: 0 0 10px 0;
                    color: #333;
                    font-size: 14px;
                }
                .png-emblem-help ul {
                    margin: 0;
                    padding-left: 20px;
                }
                .png-emblem-help li {
                    margin-bottom: 5px;
                    font-size: 13px;
                    color: #666;
                }
            `)
            .appendTo('head');

        // Enhanced media control for PNG emblem
        var emblemImageControl = wp.customize.control('png_emblem_image');
        
        if (emblemImageControl) {
            emblemImageControl.container.on('click', '.upload-button', function() {
                // Add custom media library settings
                var mediaFrame = wp.media({
                    title: 'Select PNG Emblem Image',
                    button: {
                        text: 'Use This Image'
                    },
                    multiple: false,
                    library: {
                        type: 'image'
                    }
                });
                
                mediaFrame.on('select', function() {
                    var attachment = mediaFrame.state().get('selection').first().toJSON();
                    emblemImageControl.setting.set(attachment.id);
                });
                
                mediaFrame.open();
                return false;
            });
        }

        // Add preview functionality
        wp.customize('png_emblem_image', function(setting) {
            setting.bind(function(value) {
                if (value) {
                    // Show preview in customizer
                    var attachment = wp.media.attachment(value);
                    attachment.fetch().then(function() {
                        var imageUrl = attachment.get('url');
                        var previewHtml = '<div class="emblem-preview" style="margin-top: 10px; text-align: center;">' +
                                         '<img src="' + imageUrl + '" style="max-width: 50px; max-height: 50px; border-radius: 50%; border: 2px solid #006A4E;">' +
                                         '<p style="font-size: 12px; color: #666; margin: 5px 0 0 0;">Current Emblem</p>' +
                                         '</div>';
                        
                        emblemImageControl.container.find('.emblem-preview').remove();
                        emblemImageControl.container.find('.customize-control-content').append(previewHtml);
                    });
                } else {
                    emblemImageControl.container.find('.emblem-preview').remove();
                }
            });
        });

    });

})(jQuery);
